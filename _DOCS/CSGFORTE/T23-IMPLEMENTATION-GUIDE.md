# T23 IMPLEMENTATION GUIDE: CSG Forte Credit Card Button

## CRITICAL CONTEXT
**Current Payment Structure**: The app has TWO payment processors:
1. **iCheckGateway (ACH)** - for instances: infinity, dreamcatering, nlp, rickyvoltz  
2. **Stripe (Credit Card)** - for all other instances

**CSG Forte Integration**: Add as THIRD independent processor supporting BOTH ACH and CC

## EXACT FILE MODIFICATIONS REQUIRED

### 1. MODIFY: `/Users/<USER>/Infinity/bento/_SRC/notify/_components/_core/_fields/contact-payment-sources.js`

#### A. ADD CSG Forte Button in Payment Methods Section
**Location**: Line ~63-80 (in the `if (!paymentForm)` section)

**CURRENT CODE**:
```javascript
if (!paymentForm) {
  ui.makeNode("buttons", "div", { css: "" });

  if (
    appConfig.instance === "infinity" ||
    appConfig.instance === "dreamcatering" ||
    appConfig.instance === "nlp" ||
    appConfig.instance == "rickyvoltz"
  ) {
    ui.buttons
      .makeNode("createACH", "button", {
        css: "pda-btnOutline-green",
        text: '<i class="fa fa-plus"></i> eCheck',
      })
      // ... existing iCG ACH handler
```

**ADD AFTER EXISTING BUTTONS**:
```javascript
// ADD CSG FORTE CREDIT CARD BUTTON
ui.buttons
  .makeNode("createForteCC", "button", {
    css: "pda-btnOutline-blue",
    text: '<i class="fa fa-credit-card"></i> CSG Forte Credit Card',
  })
  .notify(
    "click",
    {
      type: "paymentMethodRun",
      data: {
        run: function () {
          initiateForteCCPayment(ui, contactId, stripeId, options);
        }.bind(ui),
      },
    },
    sb.moduleId
  );
```

#### B. ADD CSG Forte Variables at Top of File
**Location**: After line ~20 (after existing variables)

**ADD**:
```javascript
// CSG Forte Configuration
var forteApiAccessId = "YOUR_API_ACCESS_ID";  // From T20 Dex registration
var forteSecureKey = "YOUR_API_SECURE_KEY";   // From T20 Dex registration  
var forteLocationId = "YOUR_LOCATION_ID";     // From T20 Dex registration
var forteSandboxUrl = "https://sandbox.forte.net/checkout/v2/js";
var forteProductionUrl = "https://checkout.forte.net/v2/js";
var forteCurrentEndpoint = forteSandboxUrl; // Switch to production later
```

#### C. ADD CSG Forte Implementation Function
**Location**: After `icgResponseHandler` function (around line ~1600)

**ADD COMPLETE FUNCTION**:
```javascript
function initiateForteCCPayment(ui, contactId, stripeId, options) {
  // Set UI references
  viewUI = ui;
  
  if (options.onSuccess && typeof options.onSuccess == 'function') {
    paymentPortalonSuccess = options.onSuccess;
  }

  // Set up UI structure
  ui.makeNode("buttons", "div", { css: "" });
  ui.makeNode("paymentSources", "div", {
    css: "ui cards",
    style: "max-width:250px; flex-flow:column-reverse;",
  });
  ui.paymentSources.makeNode("details", "div", { css: "content" });
  ui.makeNode("noItems", "headerText", {
    text: "",
    size: "xx-small", 
    css: "text-center",
  });

  // Set global variables
  currentProposalId = options.eventId;
  selectedInvoiceIds = options.selectedInvoiceIds;

  // Get fee structure
  var fees = options.feesList[0];
  var txFeePercent = 0;
  var txFeeFlat = 0;
  if (fees) {
    txFeePercent = fees.forte_cc_percent || 2.9; // Default Forte CC fee
    txFeeFlat = fees.forte_cc_flat_fee || 0.30;   // Default Forte CC fee
  } else {
    txFeePercent = 2.9;  // Default Forte CC fee
    txFeeFlat = 0.30;    // Default Forte CC fee
  }

  // Payment amount form
  ui.paymentSources.makeNode("form", "form", {
    amount: {
      name: "amount",
      label: "Payment Amount",
      type: "usd",
      value: options.invoiceBalance,
      change: function (form, value) {
        var newVal = +value.replace(/\D/g, "");
        var feeSchedule = calculatePaymentWithFees(
          parseFloat(newVal / 100),
          +txFeePercent,
          txFeeFlat,
          options.selectedInvoiceIds
        );

        $(ui.paymentSources.details.balance.selector).text(
          "Balance: $" + (newVal / 100).formatMoney()
        );
        $(ui.paymentSources.details.fees.selector).text(
          "Processing Fee: $" +
          (feeSchedule.fee / 100).formatMoney() +
          " (" +
          feeSchedule.feeDisplayText +
          "% + $" +
          (+txFeeFlat * options.selectedInvoiceIds.length).formatMoney() +
          ")"
        );
        $(ui.paymentSources.details.total.selector).html(
          "<span style='font-size:18px;font-weight:bold;'>Total Payment: $" +
          (feeSchedule.total / 100).formatMoney() +
          "</span>"
        );
      },
    },
    notes: {
      name: "notes", 
      label: "Notes",
      type: "textbox",
      rows: 5,
    },
  });

  // Calculate initial fees
  var feeSchedule = calculatePaymentWithFees(
    parseFloat(options.invoiceBalance / 100),
    txFeePercent,
    txFeeFlat,
    options.selectedInvoiceIds
  );

  // Display payment details
  ui.paymentSources.details.makeNode("balance", "div", {
    text: "Balance: $" + (options.invoiceBalance / 100).formatMoney(),
  });
  ui.paymentSources.details.makeNode("fees", "div", {
    text:
      "Processing Fee: $" +
      (feeSchedule.fee / 100).formatMoney() +
      " (" +
      feeSchedule.feeDisplayText +
      "% + $" +
      (+txFeeFlat * options.selectedInvoiceIds.length).formatMoney() +
      ")",
  });
  ui.paymentSources.details.makeNode("total", "div", {
    text:
      "<span style='font-size:18px;font-weight:bold;'>Total Payment: $" +
      (feeSchedule.total / 100).formatMoney() +
      "</span>",
  });
  ui.paymentSources.details.makeNode("totalBreak", "div", { text: "<br />" });
  ui.paymentSources.details.makeNode("btns", "div", {
    css: "ui bottom attached mini buttons",
  });

  // CSG Forte Payment Button
  ui.paymentSources.details.btns
    .makeNode("takePayment", "button", {
      text: "Pay with CSG Forte Credit Card",
      css: "pda-btn-teal",
    })
    .notify(
      "click",
      {
        type: "paymentMethodRun",
        data: {
          run: function () {
            // Get contact information
            sb.data.db.obj.getById("contacts", contactId, function (mainContact) {
              
              // Include CSG Forte script if not already loaded
              if (!window.ForteCheckout) {
                var script = document.createElement('script');
                script.src = forteCurrentEndpoint;
                script.onload = function() {
                  createFortePaymentButton(ui, mainContact, options, feeSchedule);
                };
                document.head.appendChild(script);
              } else {
                createFortePaymentButton(ui, mainContact, options, feeSchedule);
              }
            });
          },
        },
      },
      sb.moduleId
    );

  // Cancel button
  ui.paymentSources.details.btns
    .makeNode("cancelPayment", "button", {
      text: "Cancel",
      css: "pda-btn-yellow",
    })
    .notify(
      "click",
      {
        type: "paymentMethodRun",
        data: {
          run: function () {
            var fieldName;
            var paramObj = {
              stripeId: stripeId,
              contactId: contactId,
              instanceId: options.instanceId,
              verifiedSources: true,
            };

            options.paymentForm = false;
            ui.paymentSources.details.btns.cancelPayment.loading(true);

            sb.data.db.service(
              "StripeService",
              "getStripeCustomer",
              paramObj,
              function (response) {
                if (!response.customer) {
                  ui.makeNode("paymentSources", "div", {
                    css: "ui negative message",
                    text: "There was an error retrieving payment source information. Please contact your invoice provider for assistance.",
                  });
                  ui.patch();
                } else {
                  View(fieldName, ui, response, options);
                  ui.patch();
                }
              }
            );
          },
        },
      },
      sb.moduleId
    );

  ui.patch();
}

function createFortePaymentButton(ui, mainContact, options, feeSchedule) {
  // Get UTC time from Forte
  $.getJSON('https://sandbox.forte.net/checkout/getUTC?callback=?')
    .done(function (utcTime) {
      
      // Create signature string
      var signatureString = [
        forteApiAccessId,
        'sale',
        '2.0', 
        (feeSchedule.total / 100).toFixed(2),
        utcTime,
        'INV-' + currentProposalId,
        '', // customer_token
        ''  // paymethod_token
      ].join('|');

      // Generate signature (this needs server-side implementation)
      // For now, call server to generate signature
      sb.data.db.service(
        "ForteService", // This service needs to be created
        "generateSignature",
        {
          signatureString: signatureString,
          secureKey: forteSecureKey
        },
        function (signatureResponse) {
          if (signatureResponse.signature) {
            
            // Create CSG Forte payment button
            ui.makeNode("paymentSources", "div", { css: "ui" });
            ui.create.makeNode("label", "div", {
              text: "Total: $" + (feeSchedule.total / 100).toFixed(2),
            });
            ui.create.makeNode("break", "lineBreak", { spaces: 1 });
            
            // CSG Forte Checkout Button
            ui.create.makeNode("forteButton", "button", {
              css: "pda-btn-primary",
              text: "Pay with CSG Forte Credit Card",
              api_access_id: forteApiAccessId,
              location_id: forteLocationId,
              method: "sale",
              version_number: "2.0", 
              hash_method: "sha256",
              allowed_methods: "visa,mast,disc,amex",
              save_token: "true",
              button_text: "Pay with CSG Forte Credit Card",
              callback: "onForteCCCallback",
              total_amount: (feeSchedule.total / 100).toFixed(2),
              order_number: 'INV-' + currentProposalId,
              customer_token: "",
              paymethod_token: "",
              signature: signatureResponse.signature,
              utc_time: utcTime,
              billing_name: mainContact.fname + ' ' + mainContact.lname,
              billing_email_address: mainContact.email,
              billing_street_line1: mainContact.address1 || "",
              billing_locality: mainContact.city || "",
              billing_region: mainContact.state || "", 
              billing_postal_code: mainContact.zip || "",
              billing_country: "US",
              collect_shipping_address: "false",
              show_cardholder_name: "true"
            });

            // Add cancel button
            ui.create.makeNode("btns", "buttonGroup", { css: "" });
            ui.create.btns
              .makeNode("cancel", "button", {
                text: "Cancel",
                css: "mini compact basic grey",
              })
              .notify(
                "click",
                {
                  type: "paymentMethodRun", 
                  data: {
                    run: function () {
                      // Return to payment selection
                      var fieldName;
                      var paramObj = {
                        stripeId: options.stripeId,
                        contactId: options.contactId,
                        instanceId: options.instanceId,
                        verifiedSources: true,
                      };

                      sb.data.db.service(
                        "StripeService",
                        "getStripeCustomer",
                        paramObj,
                        function (response) {
                          View(fieldName, ui, response, options);
                          ui.patch();
                        }
                      );
                    },
                  },
                },
                sb.moduleId
              );

            ui.patch();
          }
        }
      );
    });
}

// CSG Forte Callback Handler
function onForteCCCallback(response) {
  var data = JSON.parse(response.data);
  
  switch(data.event) {
    case 'begin':
      // Transaction started
      $('body').toast({
        title: 'Processing Payment',
        message: 'Please wait while we process your credit card payment...',
        class: 'info',
        showIcon: 'circle notched loading',
        showProgress: 'bottom',
        displayTime: 0,
        closeIcon: false
      });
      break;
      
    case 'success':
      // Payment successful
      $('body').toast({
        title: 'Credit Card Payment Successful',
        message: 'Your payment has been processed successfully.',
        class: 'success',
        showIcon: 'circle check',
        showProgress: 'bottom',
        displayTime: 5000,
        closeIcon: true
      });
      
      // Process the successful payment
      processForteCCSuccess(data);
      break;
      
    case 'failure':
      // Payment failed
      $('body').toast({
        title: 'Payment Failed',
        message: data.response_description || 'Your payment could not be processed.',
        class: 'error',
        showIcon: 'exclamation circle',
        showProgress: 'bottom',
        displayTime: 10000,
        closeIcon: true
      });
      break;
      
    case 'abort':
      // User canceled
      $('body').toast({
        title: 'Payment Canceled',
        message: 'Payment was canceled by user.',
        class: 'warning',
        showIcon: 'times circle',
        showProgress: 'bottom', 
        displayTime: 3000,
        closeIcon: true
      });
      break;
      
    case 'error':
      // System error
      $('body').toast({
        title: 'System Error',
        message: data.msg || 'A system error occurred.',
        class: 'error',
        showIcon: 'exclamation triangle',
        showProgress: 'bottom',
        displayTime: 10000,
        closeIcon: true
      });
      break;
  }
}

function processForteCCSuccess(data) {
  // Create payment object parameters
  var paramObj = {
    instanceId: appConfig.id,
    paymentAmount: data.total_amount * 100, // Convert to cents
    proposalId: currentProposalId,
    invoiceIds: selectedInvoiceIds,
    forteResponse: data,
    processor: 'CSG_FORTE',
    paymentMethod: 'CREDIT_CARD',
    transactionId: data.trace_number,
    authorizationCode: data.authorization_code,
    last4: data.last_4,
    cardType: data.method_used,
    responseCode: data.response_code,
    responseDescription: data.response_description
  };

  // Call service to create payment object (this service needs to be created)
  sb.data.db.service(
    "ForteService", // This service needs to be created  
    "createForteCCPayment",
    paramObj,
    function (response) {
      if (response && paymentPortalonSuccess) {
        if (response.processedPayments && response.processedPayments.length > 0) {
          _.map(response.processedPayments, function (paym) {
            var inv = _.find(response.invoices, { id: paym.invoice });
            var title = 'Invoice Updated';
            var message = 'Payment of <strong>$' + (paym.amount / 100).formatMoney() + '</strong> has been applied to </br>' + inv.name.toUpperCase() + '.</br> New balance: $' + (inv.balance / 100).formatMoney();

            $('body').toast({
              title: title,
              message: message,
              class: 'blue',
              showIcon: 'dollar sign',
              showProgress: 'bottom',
              displayTime: 15000,
              closeIcon: true
            });
          });
        }

        paymentPortalonSuccess(response);
      }
    }
  );
}
```

### 2. CREATE NEW SERVICE: `/Users/<USER>/Infinity/bento/_SRC/pagoda/services/ForteService.php`

**CREATE NEW FILE**:
```php
<?php

class ForteService {
    
    private $apiAccessId;
    private $secureKey;
    private $locationId;
    private $sandboxMode;
    
    public function __construct() {
        // Load from environment variables or config
        $this->apiAccessId = $_ENV['FORTE_API_ACCESS_ID'] ?? 'sandbox_key';
        $this->secureKey = $_ENV['FORTE_SECURE_KEY'] ?? 'sandbox_secret'; 
        $this->locationId = $_ENV['FORTE_LOCATION_ID'] ?? 'sandbox_location';
        $this->sandboxMode = ($_ENV['FORTE_ENVIRONMENT'] ?? 'sandbox') === 'sandbox';
    }
    
    public function generateSignature($params) {
        try {
            $signatureString = $params['signatureString'];
            $secureKey = $params['secureKey'];
            
            $signature = hash_hmac('sha256', $signatureString, $secureKey);
            
            return [
                'success' => true,
                'signature' => $signature
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    public function createForteCCPayment($params) {
        try {
            // Extract parameters
            $instanceId = $params['instanceId'];
            $paymentAmount = $params['paymentAmount'];
            $proposalId = $params['proposalId'];
            $invoiceIds = $params['invoiceIds'];
            $forteResponse = $params['forteResponse'];
            
            // Create payment record in database
            // This follows the same pattern as StripeService and iCheckGatewayService
            $paymentObj = [
                'processor' => 'CSG_FORTE',
                'payment_method' => 'CREDIT_CARD',
                'amount' => $paymentAmount,
                'transaction_id' => $forteResponse['trace_number'],
                'authorization_code' => $forteResponse['authorization_code'],
                'last_4' => $forteResponse['last_4'],
                'card_type' => $forteResponse['method_used'],
                'response_code' => $forteResponse['response_code'],
                'response_description' => $forteResponse['response_description'],
                'status' => 'completed',
                'created_at' => date('Y-m-d H:i:s')
            ];
            
            // Apply payments to invoices (reuse existing logic)
            $processedPayments = [];
            $updatedInvoices = [];
            
            // This logic should mirror StripeService->chargeStripeConnectCustomer2
            // and iCheckGatewayService->createICGPaymentObj2
            
            return [
                'success' => true,
                'processedPayments' => $processedPayments,
                'invoices' => $updatedInvoices,
                'paymentObj' => $paymentObj
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
}
```

### 3. UPDATE CSS: Add CSG Forte Button Styling

**Location**: Find the CSS file that contains `.pda-btnOutline-green` and add:

```css
.pda-btnOutline-blue {
    background: linear-gradient(135deg, #2185d0, #1678c2);
    border: 2px solid #2185d0;
    color: white;
    padding: 10px 15px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.pda-btnOutline-blue:hover {
    background: linear-gradient(135deg, #1678c2, #1565c0);
    border-color: #1678c2;
}
```

## IMPLEMENTATION CHECKLIST

### Phase 1: Basic Button Implementation
- [ ] Add CSG Forte CC button after existing payment buttons  
- [ ] Add Forte configuration variables
- [ ] Test button renders correctly

### Phase 2: Core Functionality  
- [ ] Implement `initiateForteCCPayment()` function
- [ ] Add CSG Forte script loading
- [ ] Create signature generation endpoint
- [ ] Test payment flow

### Phase 3: Integration
- [ ] Create `ForteService.php` 
- [ ] Implement payment processing logic
- [ ] Add success/failure handling
- [ ] Test end-to-end payment

### Phase 4: Testing
- [ ] Test on localhost:8080 with sandbox
- [ ] Verify payment creation in database
- [ ] Test invoice updates
- [ ] Test email notifications

## TESTING ENVIRONMENT

**Localhost Setup**: 
- URL: `http://localhost:8080` 
- Instance: `rickyvoltz`
- Use CSG Forte sandbox credentials from T20

**Database**: Test payments should create records with:
- `processor = 'CSG_FORTE'`
- `payment_method = 'CREDIT_CARD'` 
- Transaction data from Forte response

This implementation adds CSG Forte as the third independent payment option while maintaining complete compatibility with existing iCheckGateway and Stripe functionality.