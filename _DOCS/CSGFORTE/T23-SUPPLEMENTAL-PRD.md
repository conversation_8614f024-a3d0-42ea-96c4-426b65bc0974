# T23 Supplemental PRD: CSG Forte Credit Card Button Implementation

## Executive Summary
Add CSG Forte Credit Card payment capability as a THIRD independent payment option alongside existing iCheckGateway (ACH) and Stripe (CC) methods. CSG Forte will eventually replace both legacy processors, starting with Credit Card functionality.

## Strategic Context
- **Current Payment Methods**: 
  - iCheckGateway (ACH payments)
  - Stripe (Credit Card payments)
- **New Addition**: CSG Forte (Credit Card AND ACH capabilities)
- **Migration Strategy**: Parallel implementation → gradual transition → full replacement
- **End State**: CSG Forte as single unified payment processor

## Payment Portal Evolution

### Current State (Before T23)
```
[Pay with Bank Account - iCheckGateway] [Pay with Credit Card - Stripe]
```

### Target State (After T23-T30)
```
[Pay with Bank Account - iCheckGateway] [Pay with Credit Card - Stripe] [Pay with CSG Forte Credit Card] [Pay with CSG Forte Bank Account]
```

### Future State (Post-Migration)
```
[Pay with Credit Card - CSG Forte] [Pay with Bank Account - CSG Forte]
```

## Technical Implementation Requirements

### T23.1: Add CSG Forte CC Button to Payment Portal UI

#### Payment Method Independence
CSG Forte implementation must be completely independent from existing payment methods:

```html
<div class="payment-methods-container">
    <!-- Existing iCheckGateway ACH -->
    <div class="payment-method icg-payment">
        <button class="icg-button-el">Pay with Bank Account (ACH)</button>
    </div>
    
    <!-- Existing Stripe CC -->
    <div class="payment-method stripe-payment">
        <button class="stripe-button-el">Pay with Credit Card</button>
    </div>
    
    <!-- NEW: CSG Forte CC -->
    <div class="payment-method forte-cc-payment">
        <button class="forte-cc-button-el" [forte-cc-attributes]>
            Pay with CSG Forte Credit Card
        </button>
    </div>
</div>
```

#### CSG Forte CC Button Configuration
```html
<button 
    api_access_id="YOUR_API_ACCESS_ID"
    location_id="YOUR_LOCATION_ID" 
    method="sale"
    version_number="2.0"
    hash_method="sha256"
    allowed_methods="visa,mast,disc,amex"
    save_token="true"
    button_text="Pay with CSG Forte Credit Card"
    callback="onForteCCCallback"
    total_amount=""
    total_amount_attr="edit"
    order_number=""
    customer_token=""
    paymethod_token=""
    signature=""
    utc_time=""
    billing_name=""
    billing_email_address=""
    billing_street_line1=""
    billing_locality=""
    billing_region=""
    billing_postal_code=""
    billing_country="US"
    collect_shipping_address="false"
    show_cardholder_name="true">
    Pay with CSG Forte Credit Card
</button>
```

### T23.2: Position Button as Third Payment Option

#### Three-Column Payment Layout
```html
<div class="payment-methods-grid three-methods">
    <!-- Column 1: Legacy ACH -->
    <div class="payment-method legacy-ach">
        <div class="payment-header">
            <h4>Bank Account (ACH)</h4>
            <span class="processor-label">via iCheckGateway</span>
        </div>
        <button class="icg-ach-button">[iCG Implementation]</button>
    </div>
    
    <!-- Column 2: Legacy CC -->
    <div class="payment-method legacy-cc">
        <div class="payment-header">
            <h4>Credit Card</h4>
            <span class="processor-label">via Stripe</span>
        </div>
        <button class="stripe-cc-button">[Stripe Implementation]</button>
    </div>
    
    <!-- Column 3: NEW CSG Forte CC -->
    <div class="payment-method forte-cc">
        <div class="payment-header">
            <h4>Credit Card</h4>
            <span class="processor-label">via CSG Forte</span>
        </div>
        <button class="forte-cc-button">[CSG Forte Implementation]</button>
    </div>
</div>
```

#### Responsive Design Considerations
```css
.payment-methods-grid.three-methods {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
}

@media (max-width: 768px) {
    .payment-methods-grid.three-methods {
        grid-template-columns: 1fr;
        gap: 15px;
    }
}
```

### T23.3: Apply Existing UI Patterns and Styling

#### Processor-Specific Styling
```css
.forte-cc {
    /* Base payment method styling */
    @extend .payment-method;
    
    /* CSG Forte specific branding */
    .payment-header h4 {
        color: #forte-brand-color;
    }
    
    .processor-label {
        background-color: #forte-accent-color;
        color: white;
        padding: 2px 8px;
        border-radius: 4px;
        font-size: 0.8em;
    }
}

.forte-cc-button {
    @extend .payment-button;
    background: linear-gradient(135deg, #forte-primary, #forte-secondary);
    border: 2px solid #forte-border;
}
```

#### Visual Differentiation
- **iCheckGateway**: Existing ACH styling/colors
- **Stripe**: Existing CC styling/colors  
- **CSG Forte**: New brand colors to distinguish as separate processor

### T23.4: Implement Button Click Handlers

#### Independent Payment Processing
Each payment method maintains separate processing logic:

```javascript
// Existing handlers remain unchanged
function initializeICGPayment() { /* existing iCG logic */ }
function initializeStripePayment() { /* existing Stripe logic */ }

// NEW: CSG Forte CC handler
function initializeForteCCPayment() {
    $('.forte-cc-button-el').on('click', function(e) {
        e.preventDefault();
        
        // Get fresh UTC time for Forte
        getForteUTCTime((utc) => {
            updateForteCCButton(utc);
            // Forte modal will launch automatically
        });
    });
}

// Initialize all payment methods independently
$(document).ready(function() {
    initializeICGPayment();      // Legacy ACH
    initializeStripePayment();   // Legacy CC
    initializeForteCCPayment();  // NEW: Forte CC
});
```

#### CSG Forte CC Callback Handler
```javascript
function onForteCCCallback(response) {
    var data = JSON.parse(response.data);
    
    switch(data.event) {
        case 'success':
            // Process CSG Forte CC payment success
            processForteCCSuccess(data);
            break;
            
        case 'failure':
            // Handle CSG Forte CC payment failure
            handleForteCCError(data);
            break;
            
        case 'abort':
            // User canceled CSG Forte CC payment
            resetPaymentForm();
            break;
    }
}

function processForteCCSuccess(data) {
    // Create payment record with processor = 'CSG_FORTE'
    // Follow existing payment processing patterns
    // Integration point for T25 (CC Payment Processing Integration)
}
```

## Implementation Strategy

### Phase 1: Parallel Implementation (T23-T26)
- Add CSG Forte CC as third option
- Maintain all existing functionality
- Zero disruption to current payment methods

### Phase 2: ACH Integration (T27-T30)  
- Add CSG Forte ACH as fourth option
- Four total payment methods available

### Phase 3: Migration Planning (Future)
- Gradual customer migration to CSG Forte
- Feature flags to control payment method visibility
- Data migration strategies for stored payment methods

## Database Considerations

### Payment Method Tracking
```sql
-- Payment records need processor identification
payments (
    id,
    invoice_id,
    amount,
    processor ENUM('iCheckGateway', 'Stripe', 'CSG_Forte'),
    payment_method ENUM('ACH', 'Credit_Card'),
    transaction_id,
    status,
    created_at
)
```

### Customer Payment Method Storage
```sql
-- Stored payment methods by processor
customer_payment_methods (
    id,
    customer_id,
    processor ENUM('iCheckGateway', 'Stripe', 'CSG_Forte'),
    payment_type ENUM('ACH', 'Credit_Card'),
    token,
    last_four,
    expires_on, -- CC only
    is_active
)
```

## Success Criteria for T23

1. ✅ CSG Forte CC button appears as third independent payment option
2. ✅ No disruption to existing iCheckGateway or Stripe functionality  
3. ✅ Proper visual distinction between all three processors
4. ✅ Independent click handling and callback processing
5. ✅ Foundation established for T24 (iFrame integration)

## Integration Dependencies

### Immediate (T23)
- CSG Forte Dex credentials (from T20)
- Button implementation and styling

### Next Phase (T24-T25)
- iFrame integration (T24)
- Payment processing integration (T25)
- Database schema updates for multi-processor support

### Future Phases (T27-T30)
- CSG Forte ACH implementation (mirror CC approach)
- Migration planning and execution tools

---

**CRITICAL NOTE**: This implementation establishes CSG Forte as an independent payment processor that will eventually replace both iCheckGateway and Stripe. The parallel approach ensures zero disruption during transition while building toward unified payment processing under CSG Forte.